#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test modelu pro klasifikaci frází.
Tento skript načte uložený model a centroidy a otestuje klasifikaci na několika příkladech.
"""

import os
import argparse
from Classifier import Classifier
def test_classification(model_name=None, model_path=None, threshold=0.6, test_phrases=None):
    """
    Načte model a centroidy a otestuje klasifikaci na několika příkladech.

    Args:
        model_name (str, optional): Název modelu pro inicializaci klasifikátoru.
        model_path (str, optional): Cesta k adresáři s uloženým modelem a centroidy.
                                   Pokud None, použije se automaticky generovaný název složky.
        threshold (float): Práh podobnosti pro klasifikaci (0.0 - 1.0).
        test_phrases (list, optional): Seznam frází pro testování. Pokud None, použi<PERSON><PERSON> se výchozí fráze.
    """
    # Inicializace klasifikátoru s daným názvem modelu
    classifier = Classifier(model_name=model_name if model_name else 'paraphrase-multilingual-MiniLM-L12-v2')

    # Pokud není zadána cesta k modelu, použijeme automaticky generovaný název složky
    if model_path is None:
        model_path = classifier.model_dir

    print(f"Testuji model '{model_name if model_name else 'výchozí'}' z: {model_path}")

    # Načtení modelu
    classifier.load_model(model_path, load_centroids=True)

    # Kontrola, zda byly načteny centroidy
    if classifier.category_centroids is None:
        print("Chyba: Centroidy nebyly načteny. Nelze provést testování.")
        return

    # Výpis dostupných kategorií
    categories = list(classifier.category_centroids.keys())
    print(f"\nDostupné kategorie ({len(categories)}):")
    for i, category in enumerate(categories):
        print(f"  {i+1}. {category}")

    # Pokud nejsou zadány testovací fráze, použijeme výchozí
    if test_phrases is None:
        test_phrases = [
            "Zálohová faktura",
            "Faktura - daňový doklad:",
            "suma k úhradě",
            "Objednávka",
            "splatno dne",
            "Daňový doklad",
            "dat vyst",
            "dat. usk. zdaň. plň.",
            "date of issue",
            "IČO",
            "DIČ",
            "dodací list",
            "datum splatnosti faktury",
            "Daňové datum",
            "Bankovní účet",
            "variabilní symbol",
            "Dodavatel",
            "Odběratel",
            "Fakturujeme vám",
            "Vystaveno dne",
            "doklad ze dne",
            "plátce",
            "vat date",
            "sazba daně",
            "základ daně",
            "ič",
            "IBAN",
            "zaplaťte nejpozději do",
            "základní sazba",
            "příjemce",
            "faktura ze dne",
            "proforma faktura"
        ]

    # Testování klasifikace
    print("\nVýsledky klasifikace:")
    print("-" * 80)
    print(f"{'Fráze':<30} | {'Kategorie':<25} | {'Podobnost':<10} | {'Výsledek':<10}")
    print("-" * 80)

    for phrase in test_phrases:
        category, similarity = classifier.classify(phrase)

        # Určení výsledku na základě prahu
        result = "OK" if similarity >= threshold else "Nejisté"

        # Výpis výsledku
        print(f"{phrase:<30} | {str(category):<25} | {similarity:.4f}    | {result}")

    print("-" * 80)


def main(model_name=None, threshold=0.6, test_phrases=None):
    """
    Hlavní funkce pro spuštění testování.

    Args:
        model_name (str, optional): Název modelu pro inicializaci klasifikátoru.
        threshold (float): Práh podobnosti pro klasifikaci (0.0 - 1.0).
        test_phrases (list, optional): Seznam frází pro testování. Pokud None, použijí se výchozí fráze.
    """
    # Pokud je skript spuštěn přímo z příkazové řádky, zpracujeme argumenty
    if __name__ == "__main__":
        parser = argparse.ArgumentParser(description="Test klasifikace frází pomocí natrénovaného modelu.")
        parser.add_argument("--model-name", "-n", default=None,
                            help="Název modelu pro inicializaci klasifikátoru.")
        parser.add_argument("--model-path", "-m", default=None,
                            help="Cesta k adresáři s uloženým modelem a centroidy. Pokud není zadána, použije se automaticky generovaný název složky.")
        parser.add_argument("--threshold", "-t", type=float, default=threshold,
                            help="Práh podobnosti pro klasifikaci (0.0 - 1.0).")

        args = parser.parse_args()

        # Použijeme hodnoty z argumentů nebo předané parametry
        model_name = args.model_name if args.model_name is not None else model_name
        model_path = args.model_path
        threshold = args.threshold

        # Inicializace klasifikátoru pro kontrolu existence modelu
        if model_name:
            classifier = Classifier(model_name=model_name)
            if model_path is None:
                model_path = classifier.model_dir

        # Kontrola existence adresáře s modelem, pokud je zadána cesta
        if model_path and not os.path.exists(model_path):
            print(f"Chyba: Adresář s modelem '{model_path}' neexistuje.")
            return

    # Spuštění testování
    test_classification(model_name=model_name, model_path=model_path, threshold=threshold, test_phrases=test_phrases)


if __name__ == "__main__":
    main(model_name="paraphrase-multilingual-MiniLM-L12-v2", threshold=0.7)
