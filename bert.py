# Fine-tuning pipeline
import os

from transformers import Bert<PERSON><PERSON><PERSON>okenClassification, <PERSON><PERSON><PERSON><PERSON>, trainer

# <PERSON><PERSON><PERSON><PERSON> předtrénovaného modelu
traning_data = load_training_data_from_xlsx('training_data')
model = BertForTokenClassification.from_pretrained("bert-base-multilingual-cased", num_labels=len(CLASSES))

# Příprava dat
train_dataset = [
    {"text": "Rechnungsdatum", "label": "INVOICE_DATE"},  # Němčina
    {"text": "Fecha de emisión", "label": "INVOICE_DATE"},  # <PERSON>pan<PERSON><PERSON><PERSON><PERSON>
    {"text": "Date d'émission", "label": "INVOICE_DATE"}   # Francouzština
]

# Trénink pouze 1 epochy (rychlá konvergence)
trainer.train(epochs=1, learning_rate=5e-5)


def load_training_data_from_xlsx(data_path):
    """
    Načte tréninková data z XLSX souboru s novou strukturou.
    Očekává jeden list pro každou kategorii.
    Struktura listu:
    A1: TEXT KOTVY PRO TRIPLET LOSS (popisná věta / prefix)
    A2: (ignorováno)
    B1: Hlavička (Pozitivní varianty)
    B2 a dál: TEXTY POZITIVNÍCH VARIANT
    C1: Hlavička (Negativní příklady)
    C2 a dál: TEXTY NEGATIVNÍCH PŘÍKLADŮ

    Args:
        data_path (str): Cesta k XLSX souboru nebo adresáři obsahujícímu soubor training_set.xlsx.

    Returns:
        list: Seznam slovníků ve formátu [{'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}, ...]
    """
    # Pokud je data_path adresář, přidáme název souboru
    if os.path.isdir(data_path):
        data_path = os.path.join(data_path, 'training_set.xlsx')

    training_data = []
    print(f"Načítám tréninková data ze souboru: {data_path} s novou strukturou.")

    if not os.path.exists(data_path):
        print(f"Chyba: Soubor '{data_path}' nenalezen.")
        return []

    try:
        # Načtení všech listů z XLSX souboru
        xlsx = pd.ExcelFile(data_path)
        sheet_names = xlsx.sheet_names

        if not sheet_names:
            print(f"Chyba: Soubor '{data_path}' neobsahuje žádné listy.")
            return []

        print(f"Nalezeno {len(sheet_names)} listů v souboru.")

        # Zpracování každého listu
        for sheet_name in sheet_names:
            try:
                # Načtení listu do DataFrame
                # Použijeme header=None, abychom neztratili první řádek jako hlavičku
                df = pd.read_excel(xlsx, sheet_name=sheet_name, header=None, dtype=str).replace({np.nan: None})

                # Kontrola, zda DataFrame obsahuje data a alespoň 3 sloupce
                if df.empty or len(df.columns) < 3:
                    print(
                        f"    Upozornění: List '{sheet_name}' neobsahuje dostatek sloupců nebo dat. Očekávány jsou 3 sloupce A, B, C. Přeskakuji.")
                    continue

                # Získání textu kotvy pro TripletLoss z buňky A2 (řádek 1, sloupec 0)
                # Ignorujeme první řádek, který je hlavička "Kotva"
                anchor_triplet_text = str(df.iloc[1, 0]).strip() if pd.notna(df.iloc[1, 0]) else None

                if not anchor_triplet_text:
                    print(f"    Upozornění: List '{sheet_name}' neobsahuje text kotvy v buňce A2. Přeskakuji.")
                    continue

                # Získání pozitivních variant ze sloupce B (od řádku 1, tj. druhý řádek v Excelu)
                # Ignorujeme první řádek, který je hlavička "Pozitivní varianty"
                positives_variants = [str(val).strip() for val in df.iloc[1:, 1].tolist() if
                                      pd.notna(val) and str(val).strip() != '']

                # Získání negativních příkladů ze sloupce C (od řádku 1, tj. druhý řádek v Excelu)
                # Ignorujeme první řádek, který je hlavička "Negativní příklady"
                negatives = [str(val).strip() for val in df.iloc[1:, 2].tolist() if
                             pd.notna(val) and str(val).strip() != '']

                # Pro TripletLoss potřebujeme alespoň jednu pozitivní variantu a jeden negativní příklad
                # Kotvu pro TripletLoss už máme (anchor_triplet_text)
                if not positives_variants or not negatives:
                    print(
                        f"    Upozornění: List '{sheet_name}' nemá dostatek pozitivních variant nebo negativních příkladů (alespoň jeden od každého typu). Přeskakuji.")
                    continue

                training_data.append({
                    'category_name': sheet_name,  # Název kategorie je název listu
                    'anchor_triplet': anchor_triplet_text,  # Text kotvy pro TripletLoss z A1
                    'positives_variants': positives_variants,  # Seznam pozitivních variant z B2 a dál
                    'negatives': negatives  # Seznam negativních příkladů z C2 a dál
                })

                print(
                    f"    Načteno z listu '{sheet_name}': Kategorie='{sheet_name}', Kotva pro triplet='{anchor_triplet_text}', {len(positives_variants)} pozitivních variant, {len(negatives)} negativních.")

            except Exception as e:
                print(f"    Chyba při zpracování listu '{sheet_name}': {e}")

    except Exception as e:
        print(f"Chyba při načítání souboru '{data_path}': {e}")

    print(f"Načítání dat dokončeno. Celkem {len(training_data)} sad dat pro triplet trénink.")
    # structured_data teď bude seznam těchto slovníků
    return training_data