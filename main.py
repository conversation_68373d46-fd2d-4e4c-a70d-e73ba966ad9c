# Inicializace OCR procesoru
import preview
import utils
from Classifier import Classifier
from OCRProcessor import OCRProcessor

ocr = OCRProcessor(language='ces', dpi=300)

# Načtení dokumentu
ocr.load_pdf('Faktura.pdf')  # nebo ocr.load_image('obrazek.png')

df = ocr.get_items()
#df = ocr.extract_grouped_data()
#df = utils.clean_texts(df)
df = utils.merge_texts(df)
utils.classify_batch_values(df)

# Inicializace klasifikátoru
# classifier = Classifier()
# classifier.load_model('my_document_classifier_model')

# Klasifikace textů
#df = classifier.batch_classify(
#    df=df,
#    text_column='text',
#    class_column='class',
#    similarity_column='similarity',
#    threshold=0.7
#)
preview.show_document('Faktura.pdf', df)